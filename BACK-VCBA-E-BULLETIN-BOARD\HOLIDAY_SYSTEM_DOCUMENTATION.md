# Holiday System Documentation

## Overview

The Holiday System automatically fetches and displays public holidays from external APIs, integrating them seamlessly into your e-bulletin calendar system. It supports both local (Philippines) and international holidays with automatic synchronization and management capabilities.

## Features

- ✅ **Automatic Holiday Fetching** - Fetches holidays from Nager.Date API (100+ countries)
- ✅ **Database Integration** - Stores holidays in the same table as regular calendar events
- ✅ **Multi-Country Support** - Supports Philippines and international holidays
- ✅ **Caching System** - Efficient caching to reduce API calls
- ✅ **Read-Only Holidays** - Auto-generated holidays are protected from accidental deletion
- ✅ **Customizable Display** - Holidays can be hidden/shown and descriptions customized
- ✅ **Calendar Integration** - Holidays appear alongside regular events in calendar views

## Database Schema

### New Columns Added to `school_calendar` Table

```sql
-- Holiday-specific columns
is_holiday TINYINT(1) DEFAULT 0          -- Whether this event is a holiday
holiday_type ENUM('local', 'international', 'school') -- Type of holiday
country_code VARCHAR(2)                   -- ISO country code (PH, US, etc.)
is_auto_generated TINYINT(1) DEFAULT 0    -- Whether auto-generated from API
api_source VARCHAR(50)                    -- Source API (nager.date)
local_name VARCHAR(255)                   -- Local/native name of holiday
holiday_types JSON                        -- Array of holiday types from API
is_global TINYINT(1)                      -- Whether observed globally in country
is_fixed TINYINT(1)                       -- Whether occurs on same date yearly
```

### Holiday Categories

- **Public Holidays** (Category)
  - **Philippine Holidays** (Subcategory) - Local holidays in Philippines
  - **International Holidays** (Subcategory) - Globally recognized holidays
  - **School Holidays** (Subcategory) - Institution-specific holidays

## API Endpoints

### Public Endpoints (No Authentication Required)

#### GET `/api/holidays`
Get all holidays with optional filters
```javascript
// Query parameters
{
  country_code: 'PH',        // Filter by country
  holiday_type: 'local',     // Filter by type
  year: 2025,                // Filter by year
  is_auto_generated: true,   // Filter auto-generated
  is_active: true            // Filter active holidays
}
```

#### GET `/api/holidays/countries`
Get available countries from API
```javascript
// Response
{
  "success": true,
  "data": {
    "countries": [
      { "countryCode": "PH", "name": "Philippines" },
      { "countryCode": "US", "name": "United States" }
    ]
  }
}
```

#### GET `/api/holidays/check/:country_code/:date`
Check if a specific date is a holiday
```javascript
// Example: GET /api/holidays/check/PH/2025-12-25
{
  "success": true,
  "data": {
    "date": "2025-12-25",
    "country_code": "PH",
    "is_holiday_api": true,
    "is_holiday_db": true,
    "db_holidays": [...]
  }
}
```

#### GET `/api/holidays/stats`
Get holiday statistics
```javascript
// Query: ?year=2025
{
  "success": true,
  "data": {
    "year": 2025,
    "total": 36,
    "by_type": {
      "local": 21,
      "international": 15,
      "school": 0
    },
    "by_source": {
      "auto_generated": 36,
      "manual": 0
    }
  }
}
```

### Admin Endpoints (Authentication Required)

#### POST `/api/holidays/sync`
Sync holidays from external API
```javascript
// Request body
{
  "country_code": "PH",      // Required: Country code
  "year": 2025,              // Required: Year
  "holiday_type": "local"    // Required: Holiday type
}

// Response
{
  "success": true,
  "data": {
    "countryCode": "PH",
    "year": 2025,
    "stats": {
      "total": 21,
      "created": 21,
      "updated": 0,
      "skipped": 0,
      "errors": 0
    }
  }
}
```

#### POST `/api/holidays/sync-multiple`
Sync holidays for multiple countries
```javascript
// Request body
{
  "countries": ["PH", "US", "CA"],
  "year": 2025
}
```

#### PATCH `/api/holidays/:holiday_id/toggle`
Toggle holiday visibility (activate/deactivate)

#### PATCH `/api/holidays/:holiday_id`
Update holiday settings (description, visibility, etc.)
```javascript
// Request body
{
  "description": "Custom description for this holiday",
  "is_active": true,
  "allow_comments": true,
  "is_alert": false
}
```

## Usage Examples

### 1. Sync Philippines Holidays for 2025

```bash
curl -X POST http://localhost:5000/api/holidays/sync \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "country_code": "PH",
    "year": 2025,
    "holiday_type": "local"
  }'
```

### 2. Sync Multiple Countries

```bash
curl -X POST http://localhost:5000/api/holidays/sync-multiple \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "countries": ["PH", "US"],
    "year": 2025
  }'
```

### 3. Get All Philippines Holidays

```bash
curl "http://localhost:5000/api/holidays?country_code=PH&year=2025"
```

### 4. Check if Christmas is a Holiday

```bash
curl "http://localhost:5000/api/holidays/check/PH/2025-12-25"
```

## Testing

Run the comprehensive test suite:

```bash
node scripts/test-holiday-system.js
```

This test covers:
- API connectivity
- Holiday fetching
- Database synchronization
- Calendar integration
- Caching functionality

## Configuration

### Environment Variables

No additional environment variables required. The system uses the existing database configuration.

### API Source

- **Primary API**: Nager.Date API (https://date.nager.at/api/v3)
- **Rate Limits**: No rate limits
- **Availability**: Free, no API key required
- **Coverage**: 100+ countries

## Holiday Types

The system supports different types of holidays:

- **Public** - Official public holidays
- **Bank** - Bank holidays (banks and offices closed)
- **School** - School holidays (schools closed)
- **Authorities** - Government offices closed
- **Optional** - Majority of people take day off
- **Observance** - Optional festivity, no paid day off

## Best Practices

### 1. Regular Synchronization

Set up a scheduled job to sync holidays annually:

```javascript
// Example: Sync holidays every January 1st
const cron = require('node-cron');

cron.schedule('0 0 1 1 *', async () => {
  const currentYear = new Date().getFullYear();
  await CalendarModel.syncHolidays('PH', currentYear, 'local');
  await CalendarModel.syncHolidays('US', currentYear, 'international');
});
```

### 2. Error Handling

Always handle API failures gracefully:

```javascript
try {
  const result = await CalendarModel.syncHolidays('PH', 2025, 'local');
  console.log('Sync successful:', result.stats);
} catch (error) {
  console.error('Sync failed:', error.message);
  // Fallback to cached data or manual holidays
}
```

### 3. Cache Management

The system automatically caches API responses for 24 hours. To clear cache:

```javascript
HolidayService.clearCache();
```

## Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Check internet connectivity
   - Verify Nager.Date API is accessible
   - Check firewall settings

2. **Database Sync Errors**
   - Ensure database connection is working
   - Check if holiday categories exist
   - Verify table schema is up to date

3. **Missing Holidays**
   - Check if country code is supported
   - Verify year is within API range (2000-2030)
   - Check if holidays are marked as inactive

### Debug Mode

Enable debug logging to troubleshoot issues:

```javascript
// Set LOG_LEVEL=debug in environment variables
process.env.LOG_LEVEL = 'debug';
```

## Support

For issues or questions:
1. Check the test script output
2. Review server logs
3. Verify database schema
4. Test API connectivity manually

## Future Enhancements

Planned features:
- [ ] Custom holiday creation interface
- [ ] Holiday notification system
- [ ] Multi-language holiday names
- [ ] Regional holiday support
- [ ] Holiday import/export functionality
