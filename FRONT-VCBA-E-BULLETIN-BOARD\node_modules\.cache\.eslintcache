[{"D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\index.tsx": "1", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\reportWebVitals.ts": "2", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\App.tsx": "3", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AdminAuthContext.tsx": "4", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\ToastContext.tsx": "5", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\StudentAuthContext.tsx": "6", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminDashboard.tsx": "7", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Settings.tsx": "8", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Calendar.tsx": "9", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\PostManagement.tsx": "10", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentSettings.tsx": "11", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\debug\\ApiTest.tsx": "12", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminNewsfeed.tsx": "13", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentDashboard.tsx": "14", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\StudentManagement.tsx": "15", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentNewsfeed.tsx": "16", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\index.ts": "17", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminLayout.tsx": "18", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentLayout.tsx": "19", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\index.ts": "20", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\index.ts": "21", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\Toast.tsx": "22", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\student-auth.service.ts": "23", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\admin-auth.service.ts": "24", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\announcementService.ts": "25", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\studentService.ts": "26", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarService.ts": "27", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\FacebookImageGallery.tsx": "28", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminCommentSection.tsx": "29", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\NotificationBell.tsx": "30", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\config\\constants.ts": "31", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendar.ts": "32", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useNotificationNavigation.ts": "33", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\NotificationBell.tsx": "34", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\CommentSection.tsx": "35", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useAnnouncements.ts": "36", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ImageLightbox.tsx": "37", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementModal.tsx": "38", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementViewDialog.tsx": "39", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\CalendarEventModal.tsx": "40", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminSidebar.tsx": "41", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentHeader.tsx": "42", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminHeader.tsx": "43", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentSidebar.tsx": "44", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx": "45", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\PublicRoute.tsx": "46", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ProtectedRoute.tsx": "47", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\index.ts": "48", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.service.ts": "49", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useComments.ts": "50", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationService.ts": "51", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationNavigationService.ts": "52", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendarImageUpload.ts": "53", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useMultipleImageUpload.ts": "54", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\commentDepth.ts": "55", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\formUtils.ts": "56", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\MultipleImageUpload.tsx": "57", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CascadingCategoryDropdown.tsx": "58", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CalendarImageUpload.tsx": "59", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\index.ts": "60", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminRegister\\AdminRegister.tsx": "61", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminLogin\\AdminLogin.tsx": "62", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\index.ts": "63", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\commentService.ts": "64", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\auth.service.ts": "65", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\StudentLogin.tsx": "66", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CalendarEventLikeButton.tsx": "67", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarReactionService.ts": "68", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\holidayService.ts": "69"}, {"size": 554, "mtime": 1752306944000, "results": "70", "hashOfConfig": "71"}, {"size": 419, "mtime": 1751128028000, "results": "72", "hashOfConfig": "71"}, {"size": 6159, "mtime": 1752391432000, "results": "73", "hashOfConfig": "71"}, {"size": 7830, "mtime": 1752857570939, "results": "74", "hashOfConfig": "71"}, {"size": 3769, "mtime": 1752721564000, "results": "75", "hashOfConfig": "71"}, {"size": 6136, "mtime": 1752857570952, "results": "76", "hashOfConfig": "71"}, {"size": 7447, "mtime": 1752271830000, "results": "77", "hashOfConfig": "71"}, {"size": 15866, "mtime": 1752865554796, "results": "78", "hashOfConfig": "71"}, {"size": 49022, "mtime": 1753054853267, "results": "79", "hashOfConfig": "71"}, {"size": 45894, "mtime": 1752867480062, "results": "80", "hashOfConfig": "71"}, {"size": 15127, "mtime": 1752863733791, "results": "81", "hashOfConfig": "71"}, {"size": 5757, "mtime": 1752391390000, "results": "82", "hashOfConfig": "71"}, {"size": 90663, "mtime": 1752882565811, "results": "83", "hashOfConfig": "71"}, {"size": 5269, "mtime": 1751481708000, "results": "84", "hashOfConfig": "71"}, {"size": 62476, "mtime": 1752106868000, "results": "85", "hashOfConfig": "71"}, {"size": 85392, "mtime": 1752886750228, "results": "86", "hashOfConfig": "71"}, {"size": 56, "mtime": 1751129202000, "results": "87", "hashOfConfig": "71"}, {"size": 1342, "mtime": 1751155290000, "results": "88", "hashOfConfig": "71"}, {"size": 1688, "mtime": 1751208620000, "results": "89", "hashOfConfig": "71"}, {"size": 103, "mtime": 1751140878000, "results": "90", "hashOfConfig": "71"}, {"size": 232, "mtime": 1751541102000, "results": "91", "hashOfConfig": "71"}, {"size": 3996, "mtime": 1751807708000, "results": "92", "hashOfConfig": "71"}, {"size": 8102, "mtime": 1752863733789, "results": "93", "hashOfConfig": "71"}, {"size": 10798, "mtime": 1752863733876, "results": "94", "hashOfConfig": "71"}, {"size": 17132, "mtime": 1752721564000, "results": "95", "hashOfConfig": "71"}, {"size": 6186, "mtime": 1751790574000, "results": "96", "hashOfConfig": "71"}, {"size": 14548, "mtime": 1753054617715, "results": "97", "hashOfConfig": "71"}, {"size": 8587, "mtime": 1752337674000, "results": "98", "hashOfConfig": "71"}, {"size": 23306, "mtime": 1752878817658, "results": "99", "hashOfConfig": "71"}, {"size": 12906, "mtime": 1752333634000, "results": "100", "hashOfConfig": "71"}, {"size": 5715, "mtime": 1752872506322, "results": "101", "hashOfConfig": "71"}, {"size": 8904, "mtime": 1752872731558, "results": "102", "hashOfConfig": "71"}, {"size": 8214, "mtime": 1752334762000, "results": "103", "hashOfConfig": "71"}, {"size": 12982, "mtime": 1752333550000, "results": "104", "hashOfConfig": "71"}, {"size": 16951, "mtime": 1752879455591, "results": "105", "hashOfConfig": "71"}, {"size": 15593, "mtime": 1752760674000, "results": "106", "hashOfConfig": "71"}, {"size": 12325, "mtime": 1752330204000, "results": "107", "hashOfConfig": "71"}, {"size": 27939, "mtime": 1752867316202, "results": "108", "hashOfConfig": "71"}, {"size": 24168, "mtime": 1752867418410, "results": "109", "hashOfConfig": "71"}, {"size": 27156, "mtime": 1752879483793, "results": "110", "hashOfConfig": "71"}, {"size": 6146, "mtime": 1752267324000, "results": "111", "hashOfConfig": "71"}, {"size": 9288, "mtime": 1752863733792, "results": "112", "hashOfConfig": "71"}, {"size": 11500, "mtime": 1752865556056, "results": "113", "hashOfConfig": "71"}, {"size": 5908, "mtime": 1752330316000, "results": "114", "hashOfConfig": "71"}, {"size": 2063, "mtime": 1751140856000, "results": "115", "hashOfConfig": "71"}, {"size": 2236, "mtime": 1751374982000, "results": "116", "hashOfConfig": "71"}, {"size": 4237, "mtime": 1751374890000, "results": "117", "hashOfConfig": "71"}, {"size": 230, "mtime": 1751371668000, "results": "118", "hashOfConfig": "71"}, {"size": 10813, "mtime": 1752761372000, "results": "119", "hashOfConfig": "71"}, {"size": 14506, "mtime": 1752879590759, "results": "120", "hashOfConfig": "71"}, {"size": 26448, "mtime": 1752380598000, "results": "121", "hashOfConfig": "71"}, {"size": 10147, "mtime": 1752334796000, "results": "122", "hashOfConfig": "71"}, {"size": 10510, "mtime": 1752310980000, "results": "123", "hashOfConfig": "71"}, {"size": 10877, "mtime": 1752092600000, "results": "124", "hashOfConfig": "71"}, {"size": 7318, "mtime": 1752381124000, "results": "125", "hashOfConfig": "71"}, {"size": 5263, "mtime": 1752867135753, "results": "126", "hashOfConfig": "71"}, {"size": 19520, "mtime": 1752338090000, "results": "127", "hashOfConfig": "71"}, {"size": 13444, "mtime": 1752870507174, "results": "128", "hashOfConfig": "71"}, {"size": 16870, "mtime": 1752338106000, "results": "129", "hashOfConfig": "71"}, {"size": 616, "mtime": 1752865556056, "results": "130", "hashOfConfig": "71"}, {"size": 15760, "mtime": 1752828048867, "results": "131", "hashOfConfig": "71"}, {"size": 9958, "mtime": 1751375132000, "results": "132", "hashOfConfig": "71"}, {"size": 42, "mtime": 1751129052000, "results": "133", "hashOfConfig": "71"}, {"size": 20869, "mtime": 1752885135797, "results": "134", "hashOfConfig": "71"}, {"size": 10034, "mtime": 1752860879212, "results": "135", "hashOfConfig": "71"}, {"size": 9297, "mtime": 1751476824000, "results": "136", "hashOfConfig": "71"}, {"size": 3321, "mtime": 1752876410067, "results": "137", "hashOfConfig": "71"}, {"size": 3686, "mtime": 1752890875441, "results": "138", "hashOfConfig": "71"}, {"size": 7718, "mtime": 1753054664933, "results": "139", "hashOfConfig": "71"}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ycd4oj", {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\index.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\reportWebVitals.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\App.tsx", ["347"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AdminAuthContext.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\ToastContext.tsx", ["348"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\StudentAuthContext.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Settings.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Calendar.tsx", ["349", "350", "351", "352"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\PostManagement.tsx", ["353", "354", "355"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentSettings.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\debug\\ApiTest.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminNewsfeed.tsx", ["356", "357", "358", "359", "360", "361"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentDashboard.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\StudentManagement.tsx", ["362", "363"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentNewsfeed.tsx", ["364", "365", "366", "367", "368", "369", "370"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminLayout.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentLayout.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\Toast.tsx", ["371"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\student-auth.service.ts", ["372"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\admin-auth.service.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\announcementService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\studentService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarService.ts", ["373"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\FacebookImageGallery.tsx", ["374"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminCommentSection.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\NotificationBell.tsx", ["375"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\config\\constants.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendar.ts", ["376"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useNotificationNavigation.ts", ["377"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\NotificationBell.tsx", ["378"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\CommentSection.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useAnnouncements.ts", ["379", "380", "381", "382", "383"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ImageLightbox.tsx", ["384", "385", "386", "387"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementModal.tsx", ["388"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementViewDialog.tsx", ["389", "390", "391", "392"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\CalendarEventModal.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminSidebar.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentHeader.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminHeader.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentSidebar.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\PublicRoute.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ProtectedRoute.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.service.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useComments.ts", ["393", "394", "395", "396", "397"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationService.ts", ["398", "399", "400", "401", "402"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationNavigationService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendarImageUpload.ts", ["403", "404", "405", "406", "407", "408"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useMultipleImageUpload.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\commentDepth.ts", ["409", "410", "411"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\formUtils.ts", ["412"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\MultipleImageUpload.tsx", ["413", "414"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CascadingCategoryDropdown.tsx", ["415"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CalendarImageUpload.tsx", ["416", "417", "418"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminRegister\\AdminRegister.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminLogin\\AdminLogin.tsx", ["419", "420", "421"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\commentService.ts", ["422"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\auth.service.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\StudentLogin.tsx", ["423", "424", "425"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CalendarEventLikeButton.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarReactionService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\holidayService.ts", [], [], {"ruleId": "426", "severity": 1, "message": "427", "line": 3, "column": 10, "nodeType": "428", "messageId": "429", "endLine": 3, "endColumn": 22}, {"ruleId": "426", "severity": 1, "message": "430", "line": 2, "column": 28, "nodeType": "428", "messageId": "429", "endLine": 2, "endColumn": 38}, {"ruleId": "426", "severity": 1, "message": "431", "line": 6, "column": 132, "nodeType": "428", "messageId": "429", "endLine": 6, "endColumn": 137}, {"ruleId": "432", "severity": 1, "message": "433", "line": 250, "column": 6, "nodeType": "434", "endLine": 250, "endColumn": 57, "suggestions": "435"}, {"ruleId": "432", "severity": 1, "message": "436", "line": 250, "column": 7, "nodeType": "437", "endLine": 250, "endColumn": 32}, {"ruleId": "432", "severity": 1, "message": "436", "line": 250, "column": 34, "nodeType": "437", "endLine": 250, "endColumn": 56}, {"ruleId": "426", "severity": 1, "message": "438", "line": 53, "column": 19, "nodeType": "428", "messageId": "429", "endLine": 53, "endColumn": 29}, {"ruleId": "426", "severity": 1, "message": "439", "line": 83, "column": 5, "nodeType": "428", "messageId": "429", "endLine": 83, "endColumn": 10}, {"ruleId": "432", "severity": 1, "message": "440", "line": 162, "column": 6, "nodeType": "434", "endLine": 162, "endColumn": 82, "suggestions": "441"}, {"ruleId": "426", "severity": 1, "message": "442", "line": 3, "column": 10, "nodeType": "428", "messageId": "429", "endLine": 3, "endColumn": 29}, {"ruleId": "426", "severity": 1, "message": "443", "line": 23, "column": 3, "nodeType": "428", "messageId": "429", "endLine": 23, "endColumn": 7}, {"ruleId": "432", "severity": 1, "message": "444", "line": 101, "column": 6, "nodeType": "434", "endLine": 101, "endColumn": 17, "suggestions": "445"}, {"ruleId": "426", "severity": 1, "message": "446", "line": 473, "column": 31, "nodeType": "428", "messageId": "429", "endLine": 473, "endColumn": 45}, {"ruleId": "426", "severity": 1, "message": "447", "line": 536, "column": 9, "nodeType": "428", "messageId": "429", "endLine": 536, "endColumn": 28}, {"ruleId": "426", "severity": 1, "message": "448", "line": 702, "column": 9, "nodeType": "428", "messageId": "429", "endLine": 702, "endColumn": 24}, {"ruleId": "426", "severity": 1, "message": "449", "line": 2, "column": 57, "nodeType": "428", "messageId": "429", "endLine": 2, "endColumn": 73}, {"ruleId": "426", "severity": 1, "message": "450", "line": 17, "column": 10, "nodeType": "428", "messageId": "429", "endLine": 17, "endColumn": 23}, {"ruleId": "426", "severity": 1, "message": "442", "line": 6, "column": 10, "nodeType": "428", "messageId": "429", "endLine": 6, "endColumn": 29}, {"ruleId": "426", "severity": 1, "message": "451", "line": 6, "column": 31, "nodeType": "428", "messageId": "429", "endLine": 6, "endColumn": 46}, {"ruleId": "426", "severity": 1, "message": "452", "line": 11, "column": 15, "nodeType": "428", "messageId": "429", "endLine": 11, "endColumn": 27}, {"ruleId": "432", "severity": 1, "message": "444", "line": 100, "column": 6, "nodeType": "434", "endLine": 100, "endColumn": 17, "suggestions": "453"}, {"ruleId": "426", "severity": 1, "message": "446", "line": 391, "column": 31, "nodeType": "428", "messageId": "429", "endLine": 391, "endColumn": 45}, {"ruleId": "426", "severity": 1, "message": "454", "line": 491, "column": 14, "nodeType": "428", "messageId": "429", "endLine": 491, "endColumn": 34}, {"ruleId": "426", "severity": 1, "message": "448", "line": 721, "column": 9, "nodeType": "428", "messageId": "429", "endLine": 721, "endColumn": 24}, {"ruleId": "432", "severity": 1, "message": "455", "line": 39, "column": 6, "nodeType": "434", "endLine": 39, "endColumn": 16, "suggestions": "456"}, {"ruleId": "426", "severity": 1, "message": "457", "line": 35, "column": 32, "nodeType": "428", "messageId": "429", "endLine": 35, "endColumn": 33}, {"ruleId": "426", "severity": 1, "message": "458", "line": 3, "column": 25, "nodeType": "428", "messageId": "429", "endLine": 3, "endColumn": 37}, {"ruleId": "432", "severity": 1, "message": "444", "line": 64, "column": 6, "nodeType": "434", "endLine": 64, "endColumn": 17, "suggestions": "459"}, {"ruleId": "426", "severity": 1, "message": "460", "line": 93, "column": 9, "nodeType": "428", "messageId": "429", "endLine": 93, "endColumn": 19}, {"ruleId": "426", "severity": 1, "message": "461", "line": 7, "column": 3, "nodeType": "428", "messageId": "429", "endLine": 7, "endColumn": 15}, {"ruleId": "432", "severity": 1, "message": "462", "line": 57, "column": 6, "nodeType": "434", "endLine": 57, "endColumn": 16, "suggestions": "463"}, {"ruleId": "426", "severity": 1, "message": "460", "line": 93, "column": 9, "nodeType": "428", "messageId": "429", "endLine": 93, "endColumn": 19}, {"ruleId": "426", "severity": 1, "message": "464", "line": 2, "column": 31, "nodeType": "428", "messageId": "429", "endLine": 2, "endColumn": 55}, {"ruleId": "426", "severity": 1, "message": "465", "line": 8, "column": 15, "nodeType": "428", "messageId": "429", "endLine": 8, "endColumn": 26}, {"ruleId": "432", "severity": 1, "message": "466", "line": 121, "column": 6, "nodeType": "434", "endLine": 121, "endColumn": 105, "suggestions": "467"}, {"ruleId": "432", "severity": 1, "message": "468", "line": 121, "column": 7, "nodeType": "437", "endLine": 121, "endColumn": 30}, {"ruleId": "432", "severity": 1, "message": "469", "line": 324, "column": 6, "nodeType": "434", "endLine": 324, "endColumn": 48, "suggestions": "470"}, {"ruleId": "426", "severity": 1, "message": "471", "line": 3, "column": 10, "nodeType": "428", "messageId": "429", "endLine": 3, "endColumn": 21}, {"ruleId": "432", "severity": 1, "message": "444", "line": 56, "column": 6, "nodeType": "434", "endLine": 56, "endColumn": 17, "suggestions": "472"}, {"ruleId": "426", "severity": 1, "message": "473", "line": 147, "column": 10, "nodeType": "428", "messageId": "429", "endLine": 147, "endColumn": 21}, {"ruleId": "432", "severity": 1, "message": "474", "line": 180, "column": 6, "nodeType": "434", "endLine": 180, "endColumn": 23, "suggestions": "475"}, {"ruleId": "432", "severity": 1, "message": "476", "line": 182, "column": 6, "nodeType": "434", "endLine": 182, "endColumn": 113, "suggestions": "477"}, {"ruleId": "426", "severity": 1, "message": "478", "line": 2, "column": 34, "nodeType": "428", "messageId": "429", "endLine": 2, "endColumn": 37}, {"ruleId": "432", "severity": 1, "message": "444", "line": 65, "column": 6, "nodeType": "434", "endLine": 65, "endColumn": 17, "suggestions": "479"}, {"ruleId": "480", "severity": 1, "message": "481", "line": 248, "column": 11, "nodeType": "482", "endLine": 262, "endColumn": 13}, {"ruleId": "480", "severity": 1, "message": "481", "line": 332, "column": 19, "nodeType": "482", "endLine": 346, "endColumn": 21}, {"ruleId": "426", "severity": 1, "message": "483", "line": 2, "column": 10, "nodeType": "428", "messageId": "429", "endLine": 2, "endColumn": 24}, {"ruleId": "426", "severity": 1, "message": "484", "line": 12, "column": 3, "nodeType": "428", "messageId": "429", "endLine": 12, "endColumn": 17}, {"ruleId": "426", "severity": 1, "message": "485", "line": 13, "column": 3, "nodeType": "428", "messageId": "429", "endLine": 13, "endColumn": 28}, {"ruleId": "432", "severity": 1, "message": "486", "line": 140, "column": 6, "nodeType": "434", "endLine": 140, "endColumn": 57, "suggestions": "487"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 426, "column": 44, "nodeType": "490", "messageId": "491", "endLine": 426, "endColumn": 98}, {"ruleId": "426", "severity": 1, "message": "492", "line": 1, "column": 22, "nodeType": "428", "messageId": "429", "endLine": 1, "endColumn": 37}, {"ruleId": "426", "severity": 1, "message": "493", "line": 1, "column": 39, "nodeType": "428", "messageId": "429", "endLine": 1, "endColumn": 56}, {"ruleId": "426", "severity": 1, "message": "494", "line": 2, "column": 10, "nodeType": "428", "messageId": "429", "endLine": 2, "endColumn": 26}, {"ruleId": "495", "severity": 1, "message": "496", "line": 581, "column": 3, "nodeType": "497", "messageId": "498", "endLine": 583, "endColumn": 4}, {"ruleId": "495", "severity": 1, "message": "496", "line": 647, "column": 3, "nodeType": "497", "messageId": "498", "endLine": 649, "endColumn": 4}, {"ruleId": "432", "severity": 1, "message": "499", "line": 128, "column": 6, "nodeType": "434", "endLine": 128, "endColumn": 18, "suggestions": "500"}, {"ruleId": "432", "severity": 1, "message": "501", "line": 173, "column": 6, "nodeType": "434", "endLine": 173, "endColumn": 33, "suggestions": "502"}, {"ruleId": "432", "severity": 1, "message": "501", "line": 204, "column": 6, "nodeType": "434", "endLine": 204, "endColumn": 21, "suggestions": "503"}, {"ruleId": "432", "severity": 1, "message": "501", "line": 239, "column": 6, "nodeType": "434", "endLine": 239, "endColumn": 33, "suggestions": "504"}, {"ruleId": "432", "severity": 1, "message": "505", "line": 299, "column": 6, "nodeType": "434", "endLine": 299, "endColumn": 22, "suggestions": "506"}, {"ruleId": "432", "severity": 1, "message": "507", "line": 306, "column": 6, "nodeType": "434", "endLine": 306, "endColumn": 18, "suggestions": "508"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 35, "column": 44, "nodeType": "490", "messageId": "491", "endLine": 35, "endColumn": 98}, {"ruleId": "488", "severity": 1, "message": "509", "line": 96, "column": 46, "nodeType": "490", "messageId": "491", "endLine": 96, "endColumn": 97}, {"ruleId": "510", "severity": 1, "message": "511", "line": 234, "column": 1, "nodeType": "512", "endLine": 248, "endColumn": 3}, {"ruleId": "426", "severity": 1, "message": "513", "line": 23, "column": 11, "nodeType": "428", "messageId": "429", "endLine": 23, "endColumn": 28}, {"ruleId": "432", "severity": 1, "message": "444", "line": 100, "column": 6, "nodeType": "434", "endLine": 100, "endColumn": 17, "suggestions": "514"}, {"ruleId": "432", "severity": 1, "message": "515", "line": 375, "column": 6, "nodeType": "434", "endLine": 375, "endColumn": 8, "suggestions": "516"}, {"ruleId": "517", "severity": 1, "message": "518", "line": 259, "column": 7, "nodeType": "482", "endLine": 273, "endColumn": 8}, {"ruleId": "432", "severity": 1, "message": "444", "line": 66, "column": 6, "nodeType": "434", "endLine": 66, "endColumn": 17, "suggestions": "519"}, {"ruleId": "426", "severity": 1, "message": "520", "line": 385, "column": 9, "nodeType": "428", "messageId": "429", "endLine": 385, "endColumn": 24}, {"ruleId": "432", "severity": 1, "message": "521", "line": 404, "column": 6, "nodeType": "434", "endLine": 404, "endColumn": 8, "suggestions": "522"}, {"ruleId": "426", "severity": 1, "message": "523", "line": 2, "column": 10, "nodeType": "428", "messageId": "429", "endLine": 2, "endColumn": 14}, {"ruleId": "426", "severity": 1, "message": "524", "line": 90, "column": 9, "nodeType": "428", "messageId": "429", "endLine": 90, "endColumn": 33}, {"ruleId": "426", "severity": 1, "message": "525", "line": 94, "column": 9, "nodeType": "428", "messageId": "429", "endLine": 94, "endColumn": 26}, {"ruleId": "426", "severity": 1, "message": "526", "line": 4, "column": 27, "nodeType": "428", "messageId": "429", "endLine": 4, "endColumn": 39}, {"ruleId": "426", "severity": 1, "message": "523", "line": 2, "column": 10, "nodeType": "428", "messageId": "429", "endLine": 2, "endColumn": 14}, {"ruleId": "426", "severity": 1, "message": "524", "line": 105, "column": 9, "nodeType": "428", "messageId": "429", "endLine": 105, "endColumn": 33}, {"ruleId": "426", "severity": 1, "message": "527", "line": 110, "column": 9, "nodeType": "428", "messageId": "429", "endLine": 110, "endColumn": 16}, "@typescript-eslint/no-unused-vars", "'AuthProvider' is defined but never used.", "Identifier", "unusedVar", "'ToastProps' is defined but never used.", "'Heart' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useMemo has a missing dependency: 'currentDate'. Either include it or remove the dependency array.", "ArrayExpression", ["528"], "React Hook useMemo has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", "'setFilters' is assigned a value but never used.", "'error' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filters' and 'updateFilters'. Either include them or remove the dependency array.", ["529"], "'announcementService' is defined but never used.", "'Edit' is defined but never used.", "React Hook useEffect has a missing dependency: 'imageUrl'. Either include it or remove the dependency array.", ["530"], "'notificationId' is assigned a value but never used.", "'fetchRecentStudents' is assigned a value but never used.", "'combinedContent' is assigned a value but never used.", "'StudentsResponse' is defined but never used.", "'totalStudents' is assigned a value but never used.", "'calendarService' is defined but never used.", "'Announcement' is defined but never used.", ["531"], "'refreshAnnouncements' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["532"], "'T' is defined but never used.", "'API_BASE_URL' is defined but never used.", ["533"], "'markAsRead' is assigned a value but never used.", "'EventFilters' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleDeepLinkHighlighting'. Either include it or remove the dependency array.", ["534"], "'adminAnnouncementService' is defined but never used.", "'Subcategory' is defined but never used.", "React Hook useCallback has a missing dependency: 'filters'. Either include it or remove the dependency array.", ["535"], "React Hook useCallback has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "React Hook useEffect has a missing dependency: 'fetchAnnouncements'. Either include it or remove the dependency array.", ["536"], "'getImageUrl' is defined but never used.", ["537"], "'imageLoaded' is assigned a value but never used.", "React Hook useCallback has missing dependencies: 'goToNext' and 'goToPrevious'. Either include them or remove the dependency array.", ["538"], "React Hook useEffect has missing dependencies: 'announcement' and 'refreshImages'. Either include them or remove the dependency array.", ["539"], "'Eye' is defined but never used.", ["540"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'commentService' is defined but never used.", "'CommentFilters' is defined but never used.", "'PaginatedCommentsResponse' is defined but never used.", "React Hook useCallback has an unnecessary dependency: 'calendarId'. Either exclude it or remove the dependency array.", ["541"], "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentComment'.", "ArrowFunctionExpression", "unsafeRefs", "'adminHttpClient' is defined but never used.", "'studentHttpClient' is defined but never used.", "'AdminAuthService' is defined but never used.", "@typescript-eslint/no-useless-constructor", "Useless constructor.", "MethodDefinition", "noUselessConstructor", "React Hook useCallback has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["542"], "React Hook useCallback has missing dependencies: 'onError' and 'onSuccess'. Either include them or remove the dependency array. If 'onSuccess' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["543"], ["544"], ["545"], "React Hook useCallback has a missing dependency: 'refreshImages'. Either include it or remove the dependency array.", ["546"], "React Hook useEffect has a missing dependency: 'refreshImages'. Either include it or remove the dependency array.", ["547"], "Function declared in a loop contains unsafe references to variable(s) 'rootComment'.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'skipScheduledDate' is assigned a value but never used.", ["548"], "React Hook useEffect has a missing dependency: 'images'. Either include it or remove the dependency array.", ["549"], "jsx-a11y/role-supports-aria-props", "The attribute aria-required is not supported by the role button.", ["550"], "'setPrimaryImage' is assigned a value but never used.", "React Hook React.useEffect has a missing dependency: 'images'. Either include it or remove the dependency array.", ["551"], "'Link' is defined but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "'handleForceLogout' is assigned a value but never used.", "'ReactionType' is defined but never used.", "'isEmail' is assigned a value but never used.", {"desc": "552", "fix": "553"}, {"desc": "554", "fix": "555"}, {"desc": "556", "fix": "557"}, {"desc": "556", "fix": "558"}, {"desc": "559", "fix": "560"}, {"desc": "556", "fix": "561"}, {"desc": "562", "fix": "563"}, {"desc": "564", "fix": "565"}, {"desc": "566", "fix": "567"}, {"desc": "556", "fix": "568"}, {"desc": "569", "fix": "570"}, {"desc": "571", "fix": "572"}, {"desc": "556", "fix": "573"}, {"desc": "574", "fix": "575"}, {"desc": "576", "fix": "577"}, {"desc": "578", "fix": "579"}, {"desc": "580", "fix": "581"}, {"desc": "578", "fix": "582"}, {"desc": "583", "fix": "584"}, {"desc": "585", "fix": "586"}, {"desc": "556", "fix": "587"}, {"desc": "588", "fix": "589"}, {"desc": "556", "fix": "590"}, {"desc": "588", "fix": "591"}, "Update the dependencies array to be: [currentDate]", {"range": "592", "text": "593"}, "Update the dependencies array to be: [debouncedSearchTerm, filters, selectedCategoryId, selectedStatus, updateFilters, user.grade_level]", {"range": "594", "text": "595"}, "Update the dependencies array to be: [imagePath, imageUrl]", {"range": "596", "text": "597"}, {"range": "598", "text": "597"}, "Update the dependencies array to be: [duration, handleClose]", {"range": "599", "text": "600"}, {"range": "601", "text": "597"}, "Update the dependencies array to be: [handleDeepLinkHighlighting, location]", {"range": "602", "text": "603"}, "Update the dependencies array to be: [clearCacheIfUserChanged, useAdminService, getCurrentUserContext, filters, service]", {"range": "604", "text": "605"}, "Update the dependencies array to be: [useAdminService, clearCacheIfUserChanged, fetchAnnouncements]", {"range": "606", "text": "607"}, {"range": "608", "text": "597"}, "Update the dependencies array to be: [goToNext, goToPrevious, isOpen, onClose]", {"range": "609", "text": "610"}, "Update the dependencies array to be: [announcement.announcement_id, isOpen, clearImageError, clearPendingDeletes, resetModalForNewAnnouncement, announcement, refreshImages]", {"range": "611", "text": "612"}, {"range": "613", "text": "597"}, "Update the dependencies array to be: [getCurrentUserContext, announcementId]", {"range": "614", "text": "615"}, "Update the dependencies array to be: [calendarId, onError]", {"range": "616", "text": "617"}, "Update the dependencies array to be: [calendarId, onError, onSuccess, refreshImages]", {"range": "618", "text": "619"}, "Update the dependencies array to be: [onError, onSuccess, refreshImages]", {"range": "620", "text": "621"}, {"range": "622", "text": "619"}, "Update the dependencies array to be: [pendingDeletes, refreshImages]", {"range": "623", "text": "624"}, "Update the dependencies array to be: [calendarId, refreshImages]", {"range": "625", "text": "626"}, {"range": "627", "text": "597"}, "Update the dependencies array to be: [images]", {"range": "628", "text": "629"}, {"range": "630", "text": "597"}, {"range": "631", "text": "629"}, [8876, 8927], "[currentDate]", [4777, 4853], "[debouncedSearchTerm, filters, selectedCategoryId, selectedStatus, updateFilters, user.grade_level]", [3031, 3042], "[imagePath, imageUrl]", [2967, 2978], [900, 910], "[duration, handleClose]", [1735, 1746], [2143, 2153], "[handleDeepLinkHighlighting, location]", [4408, 4507], "[clearCacheIfUserChanged, useAdminService, getCurrentUserContext, filters, service]", [11180, 11222], "[useAdminService, clearCacheIfUserChanged, fetchAnnouncements]", [1595, 1606], [4614, 4631], "[goTo<PERSON><PERSON>t, goToPrevious, isOpen, onClose]", [6335, 6442], "[announcement.announcement_id, isOpen, clearImageError, clearPendingDeletes, resetModalForNewAnnouncement, announcement, refreshImages]", [1909, 1920], [4906, 4957], "[getCurrentUserContext, announcementId]", [4049, 4061], "[calendarId, onError]", [5454, 5481], "[calendarId, onError, onSuccess, refreshImages]", [6495, 6510], "[onError, onSuccess, refreshImages]", [7671, 7698], [9871, 9887], "[pendingDeletes, refreshImages]", [10076, 10088], "[calendarId, refreshImages]", [2737, 2748], [10514, 10516], "[images]", [1825, 1836], [11634, 11636]]