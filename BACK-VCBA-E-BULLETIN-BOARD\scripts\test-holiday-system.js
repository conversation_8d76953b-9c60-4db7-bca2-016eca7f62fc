const CalendarModel = require('../src/models/CalendarModel');
const HolidayService = require('../src/services/HolidayService');

/**
 * Test script for the Holiday System
 * This script tests holiday synchronization and management functionality
 */

async function testHolidaySystem() {
  console.log('🎉 Testing Holiday System');
  console.log('========================\n');

  try {
    // Test 1: Test HolidayService API connection
    console.log('📡 Test 1: Testing API connection...');
    const countries = await HolidayService.getAvailableCountries();
    console.log(`✅ Successfully fetched ${countries.length} available countries`);
    console.log('Sample countries:', countries.slice(0, 5).map(c => `${c.countryCode} - ${c.name}`));
    console.log('');

    // Test 2: Fetch holidays for Philippines
    console.log('🇵🇭 Test 2: Fetching Philippines holidays for 2025...');
    const phHolidays = await HolidayService.fetchHolidays('PH', 2025);
    console.log(`✅ Successfully fetched ${phHolidays.length} Philippines holidays`);
    console.log('Sample holidays:');
    phHolidays.slice(0, 3).forEach(holiday => {
      console.log(`  - ${holiday.date}: ${holiday.name} (${holiday.localName})`);
    });
    console.log('');

    // Test 3: Fetch holidays for US (international)
    console.log('🇺🇸 Test 3: Fetching US holidays for 2025...');
    const usHolidays = await HolidayService.fetchHolidays('US', 2025);
    console.log(`✅ Successfully fetched ${usHolidays.length} US holidays`);
    console.log('Sample holidays:');
    usHolidays.slice(0, 3).forEach(holiday => {
      console.log(`  - ${holiday.date}: ${holiday.name}`);
    });
    console.log('');

    // Test 4: Check if specific date is a holiday
    console.log('📅 Test 4: Checking if 2025-12-25 is a holiday in PH...');
    const isChristmasHoliday = await HolidayService.isHoliday('PH', '2025-12-25');
    console.log(`✅ Christmas Day 2025 is ${isChristmasHoliday ? 'a holiday' : 'not a holiday'} in Philippines`);
    console.log('');

    // Test 5: Sync Philippines holidays to database
    console.log('💾 Test 5: Syncing Philippines holidays to database...');
    const phSyncResult = await CalendarModel.syncHolidays('PH', 2025, 'local');
    console.log('✅ Philippines holidays sync completed:');
    console.log(`  - Total: ${phSyncResult.stats.total}`);
    console.log(`  - Created: ${phSyncResult.stats.created}`);
    console.log(`  - Updated: ${phSyncResult.stats.updated}`);
    console.log(`  - Skipped: ${phSyncResult.stats.skipped}`);
    console.log(`  - Errors: ${phSyncResult.stats.errors}`);
    console.log('');

    // Test 6: Sync US holidays to database
    console.log('💾 Test 6: Syncing US holidays to database...');
    const usSyncResult = await CalendarModel.syncHolidays('US', 2025, 'international');
    console.log('✅ US holidays sync completed:');
    console.log(`  - Total: ${usSyncResult.stats.total}`);
    console.log(`  - Created: ${usSyncResult.stats.created}`);
    console.log(`  - Updated: ${usSyncResult.stats.updated}`);
    console.log(`  - Skipped: ${usSyncResult.stats.skipped}`);
    console.log(`  - Errors: ${usSyncResult.stats.errors}`);
    console.log('');

    // Test 7: Query holidays from database
    console.log('🔍 Test 7: Querying holidays from database...');
    const allHolidays = await CalendarModel.getHolidays({ year: 2025 });
    console.log(`✅ Found ${allHolidays.length} holidays in database for 2025`);
    
    const localHolidays = await CalendarModel.getHolidays({ year: 2025, holiday_type: 'local' });
    console.log(`  - Local holidays: ${localHolidays.length}`);
    
    const internationalHolidays = await CalendarModel.getHolidays({ year: 2025, holiday_type: 'international' });
    console.log(`  - International holidays: ${internationalHolidays.length}`);
    console.log('');

    // Test 8: Display sample holidays from database
    console.log('📋 Test 8: Sample holidays from database:');
    const sampleHolidays = allHolidays.slice(0, 5);
    sampleHolidays.forEach(holiday => {
      console.log(`  - ${holiday.event_date}: ${holiday.title}`);
      console.log(`    Type: ${holiday.holiday_type}, Country: ${holiday.country_code}`);
      console.log(`    Auto-generated: ${holiday.is_auto_generated ? 'Yes' : 'No'}`);
      console.log(`    Category: ${holiday.category_name} > ${holiday.subcategory_name}`);
      console.log('');
    });

    // Test 9: Test calendar integration
    console.log('📅 Test 9: Testing calendar integration...');
    const calendarEvents = await CalendarModel.getCalendarEvents(2025, 1); // January 2025
    const holidayEvents = Object.values(calendarEvents).flat().filter(event => event.is_holiday === 1);
    console.log(`✅ Found ${holidayEvents.length} holiday events in January 2025 calendar`);
    
    if (holidayEvents.length > 0) {
      console.log('Sample holiday events in calendar:');
      holidayEvents.slice(0, 3).forEach(event => {
        console.log(`  - ${event.event_date}: ${event.title} (${event.holiday_type})`);
      });
    }
    console.log('');

    // Test 10: Cache statistics
    console.log('📊 Test 10: Holiday service cache statistics...');
    const cacheStats = HolidayService.getCacheStats();
    console.log(`✅ Cache contains ${cacheStats.size} entries`);
    console.log('Cache keys:', cacheStats.keys);
    console.log('');

    console.log('🎉 All tests completed successfully!');
    console.log('✅ Holiday system is working properly');
    console.log('');
    console.log('📝 Summary:');
    console.log(`  - API connection: Working`);
    console.log(`  - Holiday fetching: Working`);
    console.log(`  - Database sync: Working`);
    console.log(`  - Calendar integration: Working`);
    console.log(`  - Caching: Working`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    process.exit(0);
  }
}

// Run the test
if (require.main === module) {
  testHolidaySystem();
}

module.exports = testHolidaySystem;
