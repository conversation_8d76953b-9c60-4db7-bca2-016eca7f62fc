const express = require('express');
const router = express.Router();
const HolidayController = require('../controllers/HolidayController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');
const { body, param, query } = require('express-validator');

/**
 * Holiday Routes
 * Handles public holiday management and synchronization
 */

// Validation rules
const syncHolidaysValidation = [
  body('country_code')
    .optional()
    .isLength({ min: 2, max: 2 })
    .withMessage('Country code must be 2 characters'),
  body('year')
    .optional()
    .isInt({ min: 2000, max: 2030 })
    .withMessage('Year must be between 2000 and 2030'),
  body('holiday_type')
    .optional()
    .isIn(['local', 'international', 'school'])
    .withMessage('Holiday type must be local, international, or school')
];

const syncMultipleValidation = [
  body('countries')
    .isArray({ min: 1 })
    .withMessage('Countries must be a non-empty array'),
  body('countries.*')
    .isLength({ min: 2, max: 2 })
    .withMessage('Each country code must be 2 characters'),
  body('year')
    .optional()
    .isInt({ min: 2000, max: 2030 })
    .withMessage('Year must be between 2000 and 2030')
];

const holidayIdValidation = [
  param('holiday_id')
    .isInt({ min: 1 })
    .withMessage('Holiday ID must be a positive integer')
];

const checkHolidayValidation = [
  param('country_code')
    .isLength({ min: 2, max: 2 })
    .withMessage('Country code must be 2 characters'),
  param('date')
    .matches(/^\d{4}-\d{2}-\d{2}$/)
    .withMessage('Date must be in YYYY-MM-DD format')
];

const updateHolidayValidation = [
  ...holidayIdValidation,
  body('description')
    .optional()
    .isString()
    .isLength({ max: 1000 })
    .withMessage('Description must be a string with max 1000 characters'),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean'),
  body('allow_comments')
    .optional()
    .isBoolean()
    .withMessage('allow_comments must be a boolean'),
  body('is_alert')
    .optional()
    .isBoolean()
    .withMessage('is_alert must be a boolean')
];

// Public routes (no authentication required)

/**
 * @route   GET /api/holidays
 * @desc    Get all holidays with optional filters
 * @access  Public
 * @query   country_code, holiday_type, year, is_auto_generated, is_active
 */
router.get('/', HolidayController.getHolidays);

/**
 * @route   GET /api/holidays/countries
 * @desc    Get available countries from API
 * @access  Public
 */
router.get('/countries', HolidayController.getAvailableCountries);

/**
 * @route   GET /api/holidays/check/:country_code/:date
 * @desc    Check if a specific date is a holiday
 * @access  Public
 * @param   country_code - ISO 3166-1 alpha-2 country code
 * @param   date - Date in YYYY-MM-DD format
 */
router.get('/check/:country_code/:date', 
  checkHolidayValidation,
  validateRequest,
  HolidayController.checkHoliday
);

/**
 * @route   GET /api/holidays/stats
 * @desc    Get holiday statistics
 * @access  Public
 * @query   year - Year for statistics (default: current year)
 */
router.get('/stats', HolidayController.getHolidayStats);

// Protected routes (authentication required)

/**
 * @route   POST /api/holidays/sync
 * @desc    Sync holidays from external API for a specific country and year
 * @access  Admin only
 * @body    country_code, year, holiday_type
 */
router.post('/sync',
  authenticateToken,
  requireAdmin,
  syncHolidaysValidation,
  validateRequest,
  HolidayController.syncHolidays
);

/**
 * @route   POST /api/holidays/sync-multiple
 * @desc    Sync holidays for multiple countries
 * @access  Admin only
 * @body    countries[], year
 */
router.post('/sync-multiple',
  authenticateToken,
  requireAdmin,
  syncMultipleValidation,
  validateRequest,
  HolidayController.syncMultipleCountries
);

/**
 * @route   PATCH /api/holidays/:holiday_id/toggle
 * @desc    Toggle holiday visibility (activate/deactivate)
 * @access  Admin only
 * @param   holiday_id - Holiday ID
 */
router.patch('/:holiday_id/toggle',
  authenticateToken,
  requireAdmin,
  holidayIdValidation,
  validateRequest,
  HolidayController.toggleHoliday
);

/**
 * @route   PATCH /api/holidays/:holiday_id
 * @desc    Update holiday description or settings
 * @access  Admin only
 * @param   holiday_id - Holiday ID
 * @body    description, is_active, allow_comments, is_alert
 */
router.patch('/:holiday_id',
  authenticateToken,
  requireAdmin,
  updateHolidayValidation,
  validateRequest,
  HolidayController.updateHoliday
);

module.exports = router;
