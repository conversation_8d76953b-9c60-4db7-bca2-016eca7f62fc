-- Migration: Add Holiday Support to Calendar System
-- This migration adds holiday-specific columns to school_calendar table
-- and creates holiday categories for better organization

-- Step 1: Add holiday-specific columns to school_calendar table
ALTER TABLE `school_calendar` 
ADD COLUMN `is_holiday` TINYINT(1) NOT NULL DEFAULT 0 
COMMENT 'Whether this event is a public holiday' 
AFTER `is_alert`,

ADD COLUMN `holiday_type` ENUM('local', 'international', 'school') DEFAULT NULL 
COMMENT 'Type of holiday: local (country-specific), international (globally recognized), school (institution-specific)' 
AFTER `is_holiday`,

ADD COLUMN `country_code` VARCHAR(2) DEFAULT NULL 
COMMENT 'ISO 3166-1 alpha-2 country code for the holiday' 
AFTER `holiday_type`,

ADD COLUMN `is_auto_generated` TINYINT(1) NOT NULL DEFAULT 0 
COMMENT 'Whether this holiday was automatically generated from API' 
AFTER `country_code`,

ADD COLUMN `api_source` VARCHAR(50) DEFAULT NULL 
COMMENT 'Source API used to generate this holiday (e.g., nager.date)' 
AFTER `is_auto_generated`,

ADD COLUMN `local_name` VARCHAR(255) DEFAULT NULL 
COMMENT 'Local/native name of the holiday' 
AFTER `api_source`,

ADD COLUMN `holiday_types` JSON DEFAULT NULL 
COMMENT 'Array of holiday types from API (Public, Bank, School, etc.)' 
AFTER `local_name`,

ADD COLUMN `is_global` TINYINT(1) DEFAULT NULL 
COMMENT 'Whether this holiday is observed globally in the country' 
AFTER `holiday_types`,

ADD COLUMN `is_fixed` TINYINT(1) DEFAULT NULL 
COMMENT 'Whether this holiday occurs on the same date every year' 
AFTER `is_global`;

-- Step 2: Add indexes for better query performance
ALTER TABLE `school_calendar`
ADD KEY `idx_calendar_is_holiday` (`is_holiday`),
ADD KEY `idx_calendar_holiday_type` (`holiday_type`),
ADD KEY `idx_calendar_country_code` (`country_code`),
ADD KEY `idx_calendar_auto_generated` (`is_auto_generated`),
ADD KEY `idx_calendar_holiday_date` (`event_date`, `is_holiday`);

-- Step 3: Insert holiday categories
INSERT INTO `categories` (`name`, `description`, `color_code`, `is_active`, `created_at`, `updated_at`) 
VALUES ('Public Holidays', 'Public holidays and observances', '#e74c3c', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
`description` = VALUES(`description`), 
`color_code` = VALUES(`color_code`), 
`updated_at` = NOW();

-- Get the category ID for public holidays
SET @holiday_category_id = (SELECT category_id FROM categories WHERE name = 'Public Holidays' LIMIT 1);

-- Step 4: Insert holiday subcategories
INSERT INTO `subcategories` (`category_id`, `name`, `description`, `color_code`, `is_active`, `display_order`, `created_at`, `updated_at`) 
VALUES 
(@holiday_category_id, 'Philippine Holidays', 'Official public holidays in the Philippines', '#e74c3c', 1, 1, NOW(), NOW()),
(@holiday_category_id, 'International Holidays', 'Globally recognized holidays and observances', '#3498db', 1, 2, NOW(), NOW()),
(@holiday_category_id, 'School Holidays', 'Institution-specific holidays and breaks', '#9b59b6', 1, 3, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
`description` = VALUES(`description`), 
`color_code` = VALUES(`color_code`), 
`updated_at` = NOW();

-- Step 5: Verify the changes
SELECT 'Holiday support columns added successfully!' as status;

-- Show the updated table structure
DESCRIBE school_calendar;

-- Show the created categories
SELECT c.name as category, s.name as subcategory, s.color_code 
FROM categories c 
LEFT JOIN subcategories s ON c.category_id = s.category_id 
WHERE c.name = 'Public Holidays';

-- Step 6: Create a view for easy holiday queries
CREATE OR REPLACE VIEW `holiday_calendar_view` AS
SELECT 
    sc.*,
    c.name as category_name,
    c.color_code as category_color,
    s.name as subcategory_name,
    s.color_code as subcategory_color,
    CASE 
        WHEN sc.is_holiday = 1 AND sc.is_auto_generated = 1 THEN 'Auto-generated Holiday'
        WHEN sc.is_holiday = 1 AND sc.is_auto_generated = 0 THEN 'Manual Holiday'
        ELSE 'Regular Event'
    END as event_type
FROM school_calendar sc
LEFT JOIN categories c ON sc.category_id = c.category_id
LEFT JOIN subcategories s ON sc.subcategory_id = s.subcategory_id
WHERE sc.is_active = 1 AND sc.deleted_at IS NULL;

SELECT 'Holiday calendar view created successfully!' as status;
