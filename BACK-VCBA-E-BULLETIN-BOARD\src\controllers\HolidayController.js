const asyncHandler = require('express-async-handler');
const CalendarModel = require('../models/CalendarModel');
const HolidayService = require('../services/HolidayService');
const { ValidationError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

/**
 * HolidayController - Manages public holiday operations
 * Handles holiday synchronization, management, and queries
 */
class HolidayController {
  
  /**
   * Get all holidays with optional filters
   * GET /api/holidays
   */
  getHolidays = asyncHandler(async (req, res) => {
    const {
      country_code,
      holiday_type,
      year,
      is_auto_generated,
      is_active
    } = req.query;

    const filters = {};
    if (country_code) filters.country_code = country_code;
    if (holiday_type) filters.holiday_type = holiday_type;
    if (year) filters.year = parseInt(year);
    if (is_auto_generated !== undefined) filters.is_auto_generated = is_auto_generated === 'true' ? 1 : 0;
    if (is_active !== undefined) filters.is_active = is_active === 'true' ? 1 : 0;

    const holidays = await CalendarModel.getHolidays(filters);

    res.status(200).json({
      success: true,
      message: 'Holidays retrieved successfully',
      data: {
        holidays,
        count: holidays.length
      }
    });
  });

  /**
   * Sync holidays from external API
   * POST /api/holidays/sync
   */
  syncHolidays = asyncHandler(async (req, res) => {
    const {
      country_code = 'PH',
      year = new Date().getFullYear(),
      holiday_type = 'local'
    } = req.body;

    // Validate inputs
    if (!country_code || country_code.length !== 2) {
      throw new ValidationError('Valid country code is required (e.g., PH, US)');
    }

    if (!year || year < 2000 || year > 2030) {
      throw new ValidationError('Valid year is required (2000-2030)');
    }

    if (!['local', 'international', 'school'].includes(holiday_type)) {
      throw new ValidationError('Holiday type must be: local, international, or school');
    }

    logger.info(`[HolidayController] Starting holiday sync for ${country_code} ${year} (${holiday_type})`);

    const result = await CalendarModel.syncHolidays(country_code, year, holiday_type);

    logger.info(`[HolidayController] Holiday sync completed:`, result.stats);

    res.status(200).json({
      success: true,
      message: `Holidays synchronized successfully for ${country_code} ${year}`,
      data: result
    });
  });

  /**
   * Sync holidays for multiple countries
   * POST /api/holidays/sync-multiple
   */
  syncMultipleCountries = asyncHandler(async (req, res) => {
    const {
      countries = ['PH'],
      year = new Date().getFullYear()
    } = req.body;

    if (!Array.isArray(countries) || countries.length === 0) {
      throw new ValidationError('Countries array is required');
    }

    const results = [];
    const errors = [];

    for (const countryCode of countries) {
      try {
        const holidayType = countryCode === 'PH' ? 'local' : 'international';
        const result = await CalendarModel.syncHolidays(countryCode, year, holidayType);
        results.push(result);
      } catch (error) {
        errors.push({
          country: countryCode,
          error: error.message
        });
      }
    }

    res.status(200).json({
      success: true,
      message: `Holiday sync completed for ${countries.length} countries`,
      data: {
        results,
        errors,
        summary: {
          total_countries: countries.length,
          successful: results.length,
          failed: errors.length
        }
      }
    });
  });

  /**
   * Get available countries from API
   * GET /api/holidays/countries
   */
  getAvailableCountries = asyncHandler(async (req, res) => {
    const countries = await HolidayService.getAvailableCountries();

    res.status(200).json({
      success: true,
      message: 'Available countries retrieved successfully',
      data: {
        countries,
        count: countries.length
      }
    });
  });

  /**
   * Check if a specific date is a holiday
   * GET /api/holidays/check/:country_code/:date
   */
  checkHoliday = asyncHandler(async (req, res) => {
    const { country_code, date } = req.params;

    // Validate date format (YYYY-MM-DD)
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(date)) {
      throw new ValidationError('Date must be in YYYY-MM-DD format');
    }

    const isHoliday = await HolidayService.isHoliday(country_code, date);

    // Also check our database for any holidays on this date
    const dbHolidays = await CalendarModel.getEventsByDate(date);
    const dbHolidayEvents = dbHolidays.filter(event => event.is_holiday === 1);

    res.status(200).json({
      success: true,
      message: 'Holiday check completed',
      data: {
        date,
        country_code,
        is_holiday_api: isHoliday,
        is_holiday_db: dbHolidayEvents.length > 0,
        db_holidays: dbHolidayEvents
      }
    });
  });

  /**
   * Toggle holiday visibility (activate/deactivate)
   * PATCH /api/holidays/:holiday_id/toggle
   */
  toggleHoliday = asyncHandler(async (req, res) => {
    const { holiday_id } = req.params;

    // Get the current holiday
    const holiday = await CalendarModel.getEventById(parseInt(holiday_id));

    if (!holiday.is_holiday) {
      throw new ValidationError('Event is not a holiday');
    }

    // Toggle the is_active status
    const newStatus = holiday.is_active === 1 ? 0 : 1;
    
    await CalendarModel.updateEvent(holiday_id, {
      is_active: newStatus
    });

    const updatedHoliday = await CalendarModel.getEventById(parseInt(holiday_id));

    res.status(200).json({
      success: true,
      message: `Holiday ${newStatus === 1 ? 'activated' : 'deactivated'} successfully`,
      data: {
        holiday: updatedHoliday
      }
    });
  });

  /**
   * Update holiday description or settings (for auto-generated holidays)
   * PATCH /api/holidays/:holiday_id
   */
  updateHoliday = asyncHandler(async (req, res) => {
    const { holiday_id } = req.params;
    const { description, is_active, allow_comments, is_alert } = req.body;

    // Get the current holiday
    const holiday = await CalendarModel.getEventById(parseInt(holiday_id));

    if (!holiday.is_holiday) {
      throw new ValidationError('Event is not a holiday');
    }

    // For auto-generated holidays, only allow certain fields to be updated
    const allowedUpdates = {};
    if (description !== undefined) allowedUpdates.description = description;
    if (is_active !== undefined) allowedUpdates.is_active = is_active ? 1 : 0;
    if (allow_comments !== undefined) allowedUpdates.allow_comments = allow_comments ? 1 : 0;
    if (is_alert !== undefined) allowedUpdates.is_alert = is_alert ? 1 : 0;

    if (Object.keys(allowedUpdates).length === 0) {
      throw new ValidationError('No valid fields to update');
    }

    await CalendarModel.updateEvent(holiday_id, allowedUpdates);

    const updatedHoliday = await CalendarModel.getEventById(parseInt(holiday_id));

    res.status(200).json({
      success: true,
      message: 'Holiday updated successfully',
      data: {
        holiday: updatedHoliday
      }
    });
  });

  /**
   * Get holiday statistics
   * GET /api/holidays/stats
   */
  getHolidayStats = asyncHandler(async (req, res) => {
    const { year = new Date().getFullYear() } = req.query;

    const stats = await this.calculateHolidayStats(parseInt(year));

    res.status(200).json({
      success: true,
      message: 'Holiday statistics retrieved successfully',
      data: stats
    });
  });

  /**
   * Calculate holiday statistics for a given year
   * @private
   */
  async calculateHolidayStats(year) {
    const allHolidays = await CalendarModel.getHolidays({ year });
    
    const stats = {
      year,
      total: allHolidays.length,
      by_type: {
        local: 0,
        international: 0,
        school: 0
      },
      by_source: {
        auto_generated: 0,
        manual: 0
      },
      by_status: {
        active: 0,
        inactive: 0
      },
      by_country: {}
    };

    allHolidays.forEach(holiday => {
      // Count by type
      if (holiday.holiday_type) {
        stats.by_type[holiday.holiday_type] = (stats.by_type[holiday.holiday_type] || 0) + 1;
      }

      // Count by source
      if (holiday.is_auto_generated === 1) {
        stats.by_source.auto_generated++;
      } else {
        stats.by_source.manual++;
      }

      // Count by status
      if (holiday.is_active === 1) {
        stats.by_status.active++;
      } else {
        stats.by_status.inactive++;
      }

      // Count by country
      if (holiday.country_code) {
        stats.by_country[holiday.country_code] = (stats.by_country[holiday.country_code] || 0) + 1;
      }
    });

    return stats;
  }
}

module.exports = new HolidayController();
