import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  Typography, 
  Button, 
  Grid, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  TextField,
  Chip,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Sync as SyncIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Edit as EditIcon,
  Info as InfoIcon,
  Flag as FlagIcon
} from '@mui/icons-material';
import { holidayService, type Holiday, type Country, type HolidayStats } from '../../services/holidayService';

interface HolidayManagementProps {
  onClose?: () => void;
}

const HolidayManagement: React.FC<HolidayManagementProps> = ({ onClose }) => {
  const [holidays, setHolidays] = useState<Holiday[]>([]);
  const [countries, setCountries] = useState<Country[]>([]);
  const [stats, setStats] = useState<HolidayStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Filters
  const [selectedCountry, setSelectedCountry] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [showActiveOnly, setShowActiveOnly] = useState(true);

  // Sync dialog
  const [syncDialogOpen, setSyncDialogOpen] = useState(false);
  const [syncCountries, setSyncCountries] = useState<string[]>(['PH']);
  const [syncYear, setSyncYear] = useState<number>(new Date().getFullYear());

  useEffect(() => {
    loadData();
  }, [selectedCountry, selectedType, selectedYear, showActiveOnly]);

  const loadData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load holidays with filters
      const holidayFilters = {
        country_code: selectedCountry || undefined,
        holiday_type: selectedType as any || undefined,
        year: selectedYear,
        is_active: showActiveOnly ? true : undefined
      };

      const [holidaysResponse, countriesResponse, statsResponse] = await Promise.all([
        holidayService.getHolidays(holidayFilters),
        holidayService.getAvailableCountries(),
        holidayService.getHolidayStats(selectedYear)
      ]);

      if (holidaysResponse.success) {
        setHolidays(holidaysResponse.data.holidays);
      }

      if (countriesResponse.success) {
        setCountries(countriesResponse.data.countries);
      }

      if (statsResponse.success) {
        setStats(statsResponse.data);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load holiday data');
    } finally {
      setLoading(false);
    }
  };

  const handleSyncHolidays = async () => {
    setSyncing(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await holidayService.syncMultipleCountries({
        countries: syncCountries,
        year: syncYear
      });

      if (response.success) {
        const { results, summary } = response.data;
        setSuccess(`Successfully synced holidays for ${summary.successful} countries. Created: ${results.reduce((sum, r) => sum + r.stats.created, 0)}, Updated: ${results.reduce((sum, r) => sum + r.stats.updated, 0)}`);
        setSyncDialogOpen(false);
        loadData(); // Refresh data
      } else {
        setError(response.message || 'Failed to sync holidays');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to sync holidays');
    } finally {
      setSyncing(false);
    }
  };

  const handleToggleHoliday = async (holidayId: number) => {
    try {
      const response = await holidayService.toggleHoliday(holidayId);
      if (response.success) {
        setSuccess('Holiday visibility updated successfully');
        loadData(); // Refresh data
      } else {
        setError(response.message || 'Failed to update holiday');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to update holiday');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getHolidayTypeColor = (type: string) => {
    switch (type) {
      case 'local': return 'primary';
      case 'international': return 'secondary';
      case 'school': return 'success';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Holiday Management
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      {/* Statistics Cards */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Holidays
                </Typography>
                <Typography variant="h4">
                  {stats.total}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Local Holidays
                </Typography>
                <Typography variant="h4" color="primary">
                  {stats.by_type.local}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  International
                </Typography>
                <Typography variant="h4" color="secondary">
                  {stats.by_type.international}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Auto-Generated
                </Typography>
                <Typography variant="h4" color="success.main">
                  {stats.by_source.auto_generated}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Controls */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                label="Year"
                type="number"
                value={selectedYear}
                onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                fullWidth
                size="small"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Country</InputLabel>
                <Select
                  value={selectedCountry}
                  onChange={(e) => setSelectedCountry(e.target.value)}
                  label="Country"
                >
                  <MenuItem value="">All Countries</MenuItem>
                  {countries.map((country) => (
                    <MenuItem key={country.countryCode} value={country.countryCode}>
                      {holidayService.getCountryFlag(country.countryCode)} {country.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Type</InputLabel>
                <Select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  label="Type"
                >
                  <MenuItem value="">All Types</MenuItem>
                  <MenuItem value="local">Local</MenuItem>
                  <MenuItem value="international">International</MenuItem>
                  <MenuItem value="school">School</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControlLabel
                control={
                  <Switch
                    checked={showActiveOnly}
                    onChange={(e) => setShowActiveOnly(e.target.checked)}
                  />
                }
                label="Active Only"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Button
                variant="contained"
                startIcon={<SyncIcon />}
                onClick={() => setSyncDialogOpen(true)}
                disabled={syncing}
                fullWidth
              >
                Sync Holidays
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Button
                variant="outlined"
                onClick={loadData}
                disabled={loading}
                fullWidth
              >
                Refresh
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Holidays Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Holidays ({holidays.length})
          </Typography>
          
          {loading ? (
            <Box display="flex" justifyContent="center" p={3}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Date</TableCell>
                    <TableCell>Holiday</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Country</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {holidays.map((holiday) => (
                    <TableRow key={holiday.calendar_id}>
                      <TableCell>
                        {formatDate(holiday.event_date)}
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {holidayService.getHolidayDisplayName(holiday)}
                          </Typography>
                          {holiday.local_name && holiday.local_name !== holiday.title && (
                            <Typography variant="caption" color="textSecondary">
                              {holiday.title}
                            </Typography>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={holidayService.getHolidayTypeDisplay(holiday.holiday_type)}
                          color={getHolidayTypeColor(holiday.holiday_type) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {holiday.country_code && (
                          <Box display="flex" alignItems="center" gap={1}>
                            <span>{holidayService.getCountryFlag(holiday.country_code)}</span>
                            <span>{holiday.country_code}</span>
                          </Box>
                        )}
                      </TableCell>
                      <TableCell>
                        <Box display="flex" alignItems="center" gap={1}>
                          {holiday.is_active ? (
                            <Chip label="Active" color="success" size="small" />
                          ) : (
                            <Chip label="Inactive" color="default" size="small" />
                          )}
                          {holiday.is_auto_generated && (
                            <Chip label="Auto" color="info" size="small" />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" gap={1}>
                          <Tooltip title={holiday.is_active ? "Hide Holiday" : "Show Holiday"}>
                            <IconButton
                              size="small"
                              onClick={() => handleToggleHoliday(holiday.calendar_id)}
                            >
                              {holiday.is_active ? <VisibilityOffIcon /> : <VisibilityIcon />}
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="View Details">
                            <IconButton size="small">
                              <InfoIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Sync Dialog */}
      <Dialog open={syncDialogOpen} onClose={() => setSyncDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Sync Holidays</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              label="Year"
              type="number"
              value={syncYear}
              onChange={(e) => setSyncYear(parseInt(e.target.value))}
              fullWidth
              sx={{ mb: 2 }}
            />
            <FormControl fullWidth>
              <InputLabel>Countries</InputLabel>
              <Select
                multiple
                value={syncCountries}
                onChange={(e) => setSyncCountries(e.target.value as string[])}
                label="Countries"
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip
                        key={value}
                        label={`${holidayService.getCountryFlag(value)} ${value}`}
                        size="small"
                      />
                    ))}
                  </Box>
                )}
              >
                <MenuItem value="PH">🇵🇭 Philippines</MenuItem>
                <MenuItem value="US">🇺🇸 United States</MenuItem>
                <MenuItem value="CA">🇨🇦 Canada</MenuItem>
                <MenuItem value="GB">🇬🇧 United Kingdom</MenuItem>
                <MenuItem value="AU">🇦🇺 Australia</MenuItem>
                <MenuItem value="JP">🇯🇵 Japan</MenuItem>
                <MenuItem value="SG">🇸🇬 Singapore</MenuItem>
                <MenuItem value="MY">🇲🇾 Malaysia</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSyncDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleSyncHolidays}
            variant="contained"
            disabled={syncing || syncCountries.length === 0}
            startIcon={syncing ? <CircularProgress size={20} /> : <SyncIcon />}
          >
            {syncing ? 'Syncing...' : 'Sync Holidays'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default HolidayManagement;
