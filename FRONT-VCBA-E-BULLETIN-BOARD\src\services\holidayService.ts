import { httpClient } from './api.service';
import { AdminAuthService } from './admin-auth.service';
import { ApiResponse } from '../types';

// Holiday-specific types
export interface Holiday {
  calendar_id: number;
  title: string;
  description?: string;
  event_date: string;
  end_date?: string;
  category_id: number;
  subcategory_id?: number;
  category_name?: string;
  category_color?: string;
  subcategory_name?: string;
  subcategory_color?: string;
  is_holiday: boolean;
  holiday_type: 'local' | 'international' | 'school';
  country_code?: string;
  is_auto_generated: boolean;
  api_source?: string;
  local_name?: string;
  holiday_types?: string[];
  is_global?: boolean;
  is_fixed?: boolean;
  is_active: boolean;
  allow_comments: boolean;
  is_alert: boolean;
  created_by: number;
  created_at: string;
  updated_at: string;
}

export interface Country {
  countryCode: string;
  name: string;
}

export interface HolidayFilters {
  country_code?: string;
  holiday_type?: 'local' | 'international' | 'school';
  year?: number;
  is_auto_generated?: boolean;
  is_active?: boolean;
}

export interface SyncHolidaysRequest {
  country_code: string;
  year: number;
  holiday_type: 'local' | 'international' | 'school';
}

export interface SyncMultipleRequest {
  countries: string[];
  year: number;
}

export interface SyncResult {
  success: boolean;
  countryCode: string;
  year: number;
  holidayType: string;
  stats: {
    total: number;
    created: number;
    updated: number;
    skipped: number;
    errors: number;
  };
  errors: string[];
}

export interface HolidayStats {
  year: number;
  total: number;
  by_type: {
    local: number;
    international: number;
    school: number;
  };
  by_source: {
    auto_generated: number;
    manual: number;
  };
  by_status: {
    active: number;
    inactive: number;
  };
  by_country: Record<string, number>;
}

export interface HolidayCheckResult {
  date: string;
  country_code: string;
  is_holiday_api: boolean;
  is_holiday_db: boolean;
  db_holidays: Holiday[];
}

export interface UpdateHolidayRequest {
  description?: string;
  is_active?: boolean;
  allow_comments?: boolean;
  is_alert?: boolean;
}

class HolidayService {
  private baseUrl = '/api/holidays';

  /**
   * Get all holidays with optional filters
   */
  async getHolidays(filters?: HolidayFilters): Promise<ApiResponse<{ holidays: Holiday[]; count: number }>> {
    const params = new URLSearchParams();
    
    if (filters?.country_code) params.append('country_code', filters.country_code);
    if (filters?.holiday_type) params.append('holiday_type', filters.holiday_type);
    if (filters?.year) params.append('year', filters.year.toString());
    if (filters?.is_auto_generated !== undefined) params.append('is_auto_generated', filters.is_auto_generated.toString());
    if (filters?.is_active !== undefined) params.append('is_active', filters.is_active.toString());

    const queryString = params.toString();
    const url = queryString ? `${this.baseUrl}?${queryString}` : this.baseUrl;

    return httpClient.get<{ holidays: Holiday[]; count: number }>(url);
  }

  /**
   * Get available countries from API
   */
  async getAvailableCountries(): Promise<ApiResponse<{ countries: Country[]; count: number }>> {
    return httpClient.get<{ countries: Country[]; count: number }>(`${this.baseUrl}/countries`);
  }

  /**
   * Check if a specific date is a holiday
   */
  async checkHoliday(countryCode: string, date: string): Promise<ApiResponse<HolidayCheckResult>> {
    return httpClient.get<HolidayCheckResult>(`${this.baseUrl}/check/${countryCode}/${date}`);
  }

  /**
   * Get holiday statistics
   */
  async getHolidayStats(year?: number): Promise<ApiResponse<HolidayStats>> {
    const params = year ? `?year=${year}` : '';
    return httpClient.get<HolidayStats>(`${this.baseUrl}/stats${params}`);
  }

  /**
   * Sync holidays from external API (Admin only)
   */
  async syncHolidays(request: SyncHolidaysRequest): Promise<ApiResponse<SyncResult>> {
    const token = AdminAuthService.getToken();
    return httpClient.post<SyncResult>(`${this.baseUrl}/sync`, request, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
  }

  /**
   * Sync holidays for multiple countries (Admin only)
   */
  async syncMultipleCountries(request: SyncMultipleRequest): Promise<ApiResponse<{ results: SyncResult[]; errors: any[]; summary: any }>> {
    const token = AdminAuthService.getToken();
    return httpClient.post<{ results: SyncResult[]; errors: any[]; summary: any }>(`${this.baseUrl}/sync-multiple`, request, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
  }

  /**
   * Toggle holiday visibility (Admin only)
   */
  async toggleHoliday(holidayId: number): Promise<ApiResponse<{ holiday: Holiday }>> {
    const token = AdminAuthService.getToken();
    return httpClient.patch<{ holiday: Holiday }>(`${this.baseUrl}/${holidayId}/toggle`, {}, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
  }

  /**
   * Update holiday settings (Admin only)
   */
  async updateHoliday(holidayId: number, request: UpdateHolidayRequest): Promise<ApiResponse<{ holiday: Holiday }>> {
    const token = AdminAuthService.getToken();
    return httpClient.patch<{ holiday: Holiday }>(`${this.baseUrl}/${holidayId}`, request, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
  }

  /**
   * Check if an event is a holiday
   */
  isHoliday(event: any): boolean {
    return event.is_holiday === true || event.is_holiday === 1;
  }

  /**
   * Check if a holiday is auto-generated
   */
  isAutoGenerated(event: any): boolean {
    return event.is_auto_generated === true || event.is_auto_generated === 1;
  }

  /**
   * Get holiday display name (prefer local name if available)
   */
  getHolidayDisplayName(event: any): string {
    if (this.isHoliday(event)) {
      return event.local_name || event.title;
    }
    return event.title;
  }

  /**
   * Get holiday type display text
   */
  getHolidayTypeDisplay(holidayType?: string): string {
    switch (holidayType) {
      case 'local':
        return 'Local Holiday';
      case 'international':
        return 'International Holiday';
      case 'school':
        return 'School Holiday';
      default:
        return 'Holiday';
    }
  }

  /**
   * Get country flag emoji
   */
  getCountryFlag(countryCode?: string): string {
    const flags: Record<string, string> = {
      'PH': '🇵🇭',
      'US': '🇺🇸',
      'CA': '🇨🇦',
      'GB': '🇬🇧',
      'AU': '🇦🇺',
      'JP': '🇯🇵',
      'KR': '🇰🇷',
      'CN': '🇨🇳',
      'IN': '🇮🇳',
      'SG': '🇸🇬',
      'MY': '🇲🇾',
      'TH': '🇹🇭',
      'VN': '🇻🇳',
      'ID': '🇮🇩',
      'FR': '🇫🇷',
      'DE': '🇩🇪',
      'IT': '🇮🇹',
      'ES': '🇪🇸',
      'NL': '🇳🇱',
      'BR': '🇧🇷',
      'MX': '🇲🇽',
      'AR': '🇦🇷'
    };
    return flags[countryCode || ''] || '🌍';
  }

  /**
   * Get holiday CSS classes for styling
   */
  getHolidayClasses(event: any): string[] {
    const classes: string[] = [];
    
    if (this.isHoliday(event)) {
      classes.push('holiday-event');
      
      if (event.holiday_type) {
        classes.push(`holiday-${event.holiday_type}`);
      }
      
      if (this.isAutoGenerated(event)) {
        classes.push('holiday-auto-generated');
      }
      
      if (event.country_code) {
        classes.push(`holiday-${event.country_code.toLowerCase()}`);
      }
    }
    
    return classes;
  }
}

export const holidayService = new HolidayService();
