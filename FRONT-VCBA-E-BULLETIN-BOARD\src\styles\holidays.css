/* Holiday-specific styles for calendar events */

/* Base holiday event styling */
.holiday-event {
  position: relative;
  border-left-width: 4px !important;
  font-weight: 600 !important;
}

.holiday-event::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  pointer-events: none;
  border-radius: inherit;
}

/* Holiday type specific styling */
.holiday-local {
  border-left-color: #dc2626 !important;
  background-color: rgba(220, 38, 38, 0.1) !important;
  border-color: #dc2626 !important;
}

.holiday-local:hover {
  background-color: rgba(220, 38, 38, 0.2) !important;
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3) !important;
}

.holiday-international {
  border-left-color: #2563eb !important;
  background-color: rgba(37, 99, 235, 0.1) !important;
  border-color: #2563eb !important;
}

.holiday-international:hover {
  background-color: rgba(37, 99, 235, 0.2) !important;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3) !important;
}

.holiday-school {
  border-left-color: #7c3aed !important;
  background-color: rgba(124, 58, 237, 0.1) !important;
  border-color: #7c3aed !important;
}

.holiday-school:hover {
  background-color: rgba(124, 58, 237, 0.2) !important;
  box-shadow: 0 2px 8px rgba(124, 58, 237, 0.3) !important;
}

/* Auto-generated holiday styling */
.holiday-auto-generated {
  border-style: dashed !important;
  position: relative;
}

.holiday-auto-generated::after {
  content: '🤖';
  position: absolute;
  top: -2px;
  right: -2px;
  font-size: 0.6rem;
  background: white;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Country-specific styling */
.holiday-ph {
  background-image: linear-gradient(45deg, rgba(220, 38, 38, 0.05) 25%, transparent 25%, transparent 75%, rgba(220, 38, 38, 0.05) 75%);
  background-size: 8px 8px;
}

.holiday-us {
  background-image: linear-gradient(45deg, rgba(37, 99, 235, 0.05) 25%, transparent 25%, transparent 75%, rgba(37, 99, 235, 0.05) 75%);
  background-size: 8px 8px;
}

/* Holiday calendar cell styling */
.calendar-cell-holiday {
  background-color: rgba(220, 38, 38, 0.02) !important;
  border-color: rgba(220, 38, 38, 0.1) !important;
}

.calendar-cell-holiday:hover {
  background-color: rgba(220, 38, 38, 0.05) !important;
}

/* Holiday event in list view */
.holiday-list-item {
  border-left: 4px solid #dc2626;
  background: linear-gradient(90deg, rgba(220, 38, 38, 0.05) 0%, transparent 100%);
}

.holiday-list-item .holiday-badge {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  box-shadow: 0 1px 3px rgba(220, 38, 38, 0.3);
}

/* Holiday type badges */
.holiday-type-local {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
}

.holiday-type-international {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  color: white;
}

.holiday-type-school {
  background: linear-gradient(135deg, #7c3aed, #6d28d9);
  color: white;
}

/* Holiday modal styling */
.holiday-modal .modal-header {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
}

.holiday-modal .holiday-info {
  background: rgba(220, 38, 38, 0.05);
  border: 1px solid rgba(220, 38, 38, 0.1);
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

.holiday-modal .holiday-readonly-notice {
  background: rgba(251, 191, 36, 0.1);
  border: 1px solid rgba(251, 191, 36, 0.3);
  border-radius: 8px;
  padding: 0.75rem;
  color: #92400e;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.holiday-modal .holiday-readonly-notice::before {
  content: '🔒';
  font-size: 1rem;
}

/* Holiday statistics cards */
.holiday-stats-card {
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.1), rgba(220, 38, 38, 0.05));
  border: 1px solid rgba(220, 38, 38, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.holiday-stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
}

.holiday-stats-card .stats-number {
  font-size: 2rem;
  font-weight: 700;
  color: #dc2626;
  margin-bottom: 0.5rem;
}

.holiday-stats-card .stats-label {
  font-size: 0.875rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Holiday sync button */
.holiday-sync-button {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.holiday-sync-button:hover {
  background: linear-gradient(135deg, #b91c1c, #991b1b);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.holiday-sync-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Holiday loading animation */
.holiday-loading {
  display: inline-block;
  animation: holidayPulse 2s infinite;
}

@keyframes holidayPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

/* Holiday notification */
.holiday-notification {
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.1), rgba(220, 38, 38, 0.05));
  border-left: 4px solid #dc2626;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  color: #374151;
}

.holiday-notification .notification-icon {
  color: #dc2626;
  font-size: 1.25rem;
  margin-right: 0.75rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .holiday-event {
    font-size: 0.7rem;
    padding: 0.125rem 0.25rem;
  }
  
  .holiday-auto-generated::after {
    display: none;
  }
  
  .holiday-stats-card {
    padding: 1rem;
  }
  
  .holiday-stats-card .stats-number {
    font-size: 1.5rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .holiday-event {
    background-color: rgba(220, 38, 38, 0.15) !important;
    color: #f9fafb !important;
  }
  
  .holiday-modal .holiday-info {
    background: rgba(220, 38, 38, 0.1);
    border-color: rgba(220, 38, 38, 0.2);
  }
  
  .holiday-notification {
    background: rgba(220, 38, 38, 0.15);
    color: #f9fafb;
  }
}
