const https = require('https');
const { ValidationError, NotFoundError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

/**
 * HolidayService - Manages public holidays from external APIs
 * Supports fetching holidays from Nager.Date API with caching and error handling
 */
class HolidayService {
  constructor() {
    this.baseUrl = 'https://date.nager.at/api/v3';
    this.cache = new Map(); // Simple in-memory cache
    this.cacheTimeout = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
  }

  /**
   * Fetch holidays for a specific country and year
   * @param {string} countryCode - ISO 3166-1 alpha-2 country code (e.g., 'PH', 'US')
   * @param {number} year - Year to fetch holidays for
   * @returns {Promise<Array>} Array of holiday objects
   */
  async fetchHolidays(countryCode, year) {
    try {
      const cacheKey = `${countryCode}-${year}`;
      
      // Check cache first
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          logger.info(`[HolidayService] Using cached holidays for ${countryCode} ${year}`);
          return cached.data;
        }
      }

      logger.info(`[HolidayService] Fetching holidays for ${countryCode} ${year} from API`);
      
      const url = `${this.baseUrl}/publicholidays/${year}/${countryCode.toUpperCase()}`;
      const holidays = await this.makeHttpRequest(url);

      // Cache the result
      this.cache.set(cacheKey, {
        data: holidays,
        timestamp: Date.now()
      });

      logger.info(`[HolidayService] Successfully fetched ${holidays.length} holidays for ${countryCode} ${year}`);
      return holidays;

    } catch (error) {
      logger.error(`[HolidayService] Error fetching holidays for ${countryCode} ${year}:`, error.message);
      throw new ValidationError(`Failed to fetch holidays: ${error.message}`);
    }
  }

  /**
   * Get available countries from the API
   * @returns {Promise<Array>} Array of country objects with codes and names
   */
  async getAvailableCountries() {
    try {
      const cacheKey = 'available-countries';
      
      // Check cache first
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          return cached.data;
        }
      }

      const url = `${this.baseUrl}/AvailableCountries`;
      const countries = await this.makeHttpRequest(url);

      // Cache the result
      this.cache.set(cacheKey, {
        data: countries,
        timestamp: Date.now()
      });

      return countries;

    } catch (error) {
      logger.error('[HolidayService] Error fetching available countries:', error.message);
      throw new ValidationError(`Failed to fetch available countries: ${error.message}`);
    }
  }

  /**
   * Check if a specific date is a holiday in a country
   * @param {string} countryCode - ISO 3166-1 alpha-2 country code
   * @param {string} date - Date in YYYY-MM-DD format
   * @returns {Promise<boolean>} True if the date is a holiday
   */
  async isHoliday(countryCode, date) {
    try {
      const url = `${this.baseUrl}/IsTodayPublicHoliday/${countryCode.toUpperCase()}?date=${date}`;
      
      return new Promise((resolve, reject) => {
        const request = https.get(url, (response) => {
          if (response.statusCode === 200) {
            resolve(true); // Date is a holiday
          } else if (response.statusCode === 204) {
            resolve(false); // Date is not a holiday
          } else {
            reject(new Error(`API returned status code: ${response.statusCode}`));
          }
        });

        request.on('error', (error) => {
          reject(error);
        });

        request.setTimeout(10000, () => {
          request.destroy();
          reject(new Error('Request timeout'));
        });
      });

    } catch (error) {
      logger.error(`[HolidayService] Error checking if ${date} is holiday in ${countryCode}:`, error.message);
      return false; // Default to false on error
    }
  }

  /**
   * Transform API holiday data to match our database schema
   * @param {Object} apiHoliday - Holiday object from API
   * @param {string} holidayType - Type of holiday ('local', 'international', 'school')
   * @param {number} categoryId - Category ID for the holiday
   * @param {number} subcategoryId - Subcategory ID for the holiday
   * @returns {Object} Transformed holiday object for database insertion
   */
  transformHolidayForDatabase(apiHoliday, holidayType, categoryId, subcategoryId) {
    return {
      title: apiHoliday.name || apiHoliday.localName,
      description: `${apiHoliday.localName ? `Local: ${apiHoliday.localName}\n` : ''}English: ${apiHoliday.name}\nCountry: ${apiHoliday.countryCode}${apiHoliday.types ? `\nTypes: ${apiHoliday.types.join(', ')}` : ''}`,
      event_date: apiHoliday.date,
      end_date: apiHoliday.date, // Holidays are typically single-day events
      category_id: categoryId,
      subcategory_id: subcategoryId,
      is_recurring: apiHoliday.fixed ? 1 : 0,
      recurrence_pattern: apiHoliday.fixed ? 'yearly' : null,
      is_active: 1,
      is_published: 1,
      allow_comments: 1,
      is_alert: 0,
      is_holiday: 1,
      holiday_type: holidayType,
      country_code: apiHoliday.countryCode,
      is_auto_generated: 1,
      api_source: 'nager.date',
      local_name: apiHoliday.localName,
      holiday_types: apiHoliday.types ? JSON.stringify(apiHoliday.types) : null,
      is_global: apiHoliday.global ? 1 : 0,
      is_fixed: apiHoliday.fixed ? 1 : 0,
      created_by: 1, // System user ID
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  /**
   * Make HTTP request to external API
   * @param {string} url - URL to request
   * @returns {Promise<Object>} Parsed JSON response
   */
  makeHttpRequest(url) {
    return new Promise((resolve, reject) => {
      const request = https.get(url, (response) => {
        let data = '';

        response.on('data', (chunk) => {
          data += chunk;
        });

        response.on('end', () => {
          try {
            if (response.statusCode === 200) {
              const parsedData = JSON.parse(data);
              resolve(parsedData);
            } else {
              reject(new Error(`HTTP ${response.statusCode}: ${data}`));
            }
          } catch (error) {
            reject(new Error(`Failed to parse JSON response: ${error.message}`));
          }
        });
      });

      request.on('error', (error) => {
        reject(error);
      });

      // Set timeout for the request
      request.setTimeout(15000, () => {
        request.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  /**
   * Clear cache (useful for testing or manual refresh)
   */
  clearCache() {
    this.cache.clear();
    logger.info('[HolidayService] Cache cleared');
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

module.exports = new HolidayService();
